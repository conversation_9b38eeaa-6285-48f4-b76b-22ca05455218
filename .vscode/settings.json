{"editor.tabSize": 2, "editor.codeActionsOnSave": {"source.organizeImports": "explicit", "source.fixAll": "explicit"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "typescript.preferences.importModuleSpecifier": "relative", "cSpell.words": ["<PERSON><PERSON><PERSON>", "deepseek", "defi", "lazyload", "partialize", "qwen", "Watchlist", "wechat"]}