export default defineAppConfig({
  pages: [
    'pages/index/index',
    'pages/news/index',
    'pages/news-details/index',
    'pages/crypto/index',
    'pages/market-analysis/index',
    'pages/profile/index',
  ],
  // tabBar: {
  //   color: '#666666',
  //   selectedColor: '#1976d2',
  //   backgroundColor: '#ffffff',
  //   borderStyle: 'black',
  //   list: [
  //     {
  //       pagePath: 'pages/index/index',
  //       text: '首页',
  //       iconPath: 'assets/icons/home.png',
  //       selectedIconPath: 'assets/icons/home-active.png',
  //     },
  //     {
  //       pagePath: 'pages/news/index',
  //       text: '资讯',
  //       iconPath: 'assets/icons/news.png',
  //       selectedIconPath: 'assets/icons/news-active.png',
  //     },
  //     {
  //       pagePath: 'pages/crypto/index',
  //       text: '行情',
  //       iconPath: 'assets/icons/crypto.png',
  //       selectedIconPath: 'assets/icons/crypto-active.png',
  //     },
  //     {
  //       pagePath: 'pages/trends/index',
  //       text: '趋势',
  //       iconPath: 'assets/icons/trends.png',
  //       selectedIconPath: 'assets/icons/trends-active.png',
  //     },
  //     {
  //       pagePath: 'pages/profile/index',
  //       text: '我的',
  //       iconPath: 'assets/icons/profile.png',
  //       selectedIconPath: 'assets/icons/profile-active.png',
  //     },
  //   ],
  // },
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#ffffff',
    navigationBarTitleText: '加密货币资讯',
    navigationBarTextStyle: 'black',
    backgroundColor: '#f8f8f8',
    enablePullDownRefresh: true,
    onReachBottomDistance: 50,
  },
  permission: {
    'scope.userLocation': {
      desc: '您的位置信息将用于小程序位置接口的效果展示',
    },
  },
  requiredPrivateInfos: ['getLocation'],
  lazyCodeLoading: 'requiredComponents',
});
