// 模拟数据服务
export const mockData = {
  // 模拟加密货币配置数据
  coins: [
    {
      id: 'bitcoin',
      name: 'Bitcoin',
      symbol: 'BTC',
      image: 'https://assets.coingecko.com/coins/images/1/large/bitcoin.png',
      current_price: 45000,
      market_cap: 850000000000,
      price_change_percentage_24h: 2.5,
      followed: true,
    },
    {
      id: 'ethereum',
      name: 'Ethereum',
      symbol: 'ETH',
      image: 'https://assets.coingecko.com/coins/images/279/large/ethereum.png',
      current_price: 3200,
      market_cap: 380000000000,
      price_change_percentage_24h: -1.2,
      followed: true,
    },
    {
      id: 'binancecoin',
      name: 'BN<PERSON>',
      symbol: 'BNB',
      image: 'https://assets.coingecko.com/coins/images/825/large/bnb-icon2_2x.png',
      current_price: 320,
      market_cap: 50000000000,
      price_change_percentage_24h: 0.8,
      followed: false,
    },
    {
      id: 'cardano',
      name: 'Cardano',
      symbol: 'ADA',
      image: 'https://assets.coingecko.com/coins/images/975/large/cardano.png',
      current_price: 0.45,
      market_cap: 15000000000,
      price_change_percentage_24h: 3.2,
      followed: false,
    },
    {
      id: 'solana',
      name: 'Solana',
      symbol: 'SOL',
      image: 'https://assets.coingecko.com/coins/images/4128/large/solana.png',
      current_price: 95,
      market_cap: 40000000000,
      price_change_percentage_24h: -2.1,
      followed: true,
    },
  ],

  // 模拟新闻数据
  news: [
    {
      id: '1',
      title: 'Bitcoin 价格突破 45,000 美元，市场情绪乐观',
      summary: '比特币价格在过去24小时内上涨2.5%，突破45,000美元关键阻力位，分析师认为这是牛市信号...',
      content: '比特币价格在过去24小时内表现强劲，成功突破45,000美元的关键阻力位。这一突破被市场分析师视为积极信号，表明加密货币市场可能正在进入新的上涨周期。',
      source: 'CoinDesk',
      author: '张三',
      published_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      image_url: 'https://via.placeholder.com/400x200/1976d2/ffffff?text=Bitcoin+News',
      tags: ['Bitcoin', 'BTC', '价格分析'],
      sentiment: 'positive' as const,
      related_coins: ['bitcoin'],
    },
    {
      id: '2',
      title: 'Ethereum 2.0 升级进展顺利，质押量创新高',
      summary: 'Ethereum 2.0 网络升级持续推进，质押的 ETH 数量达到历史新高，显示出社区对升级的信心...',
      content: 'Ethereum 2.0 网络升级正在按计划进行，目前质押的 ETH 数量已经达到历史新高。这表明以太坊社区对即将到来的升级充满信心。',
      source: 'Ethereum Foundation',
      author: '李四',
      published_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
      image_url: 'https://via.placeholder.com/400x200/6366f1/ffffff?text=Ethereum+News',
      tags: ['Ethereum', 'ETH', 'ETH2.0'],
      sentiment: 'positive' as const,
      related_coins: ['ethereum'],
    },
    {
      id: '3',
      title: '监管机构加强对加密货币交易所的监管',
      summary: '多国监管机构表示将加强对加密货币交易所的监管，要求更严格的合规措施...',
      content: '全球多个国家的监管机构近期表示，将加强对加密货币交易所的监管力度，要求交易所实施更严格的合规措施。',
      source: 'Reuters',
      author: '王五',
      published_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
      image_url: 'https://via.placeholder.com/400x200/dc2626/ffffff?text=Regulation+News',
      tags: ['监管', '合规', '交易所'],
      sentiment: 'negative' as const,
      related_coins: [],
    },
    {
      id: '4',
      title: 'DeFi 协议总锁仓价值突破 1000 亿美元',
      summary: '去中心化金融(DeFi)协议的总锁仓价值(TVL)首次突破1000亿美元大关，创历史新高...',
      content: '去中心化金融(DeFi)生态系统继续快速发展，总锁仓价值(TVL)首次突破1000亿美元大关，这标志着DeFi已经成为传统金融的重要补充。',
      source: 'DeFi Pulse',
      author: '赵六',
      published_at: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
      image_url: 'https://via.placeholder.com/400x200/059669/ffffff?text=DeFi+News',
      tags: ['DeFi', 'TVL', '去中心化金融'],
      sentiment: 'positive' as const,
      related_coins: ['ethereum'],
    },
    {
      id: '5',
      title: 'NFT 市场交易量大幅下降，泡沫破裂？',
      summary: 'NFT 市场交易量在过去一个月内下降了60%，引发市场对NFT泡沫是否破裂的讨论...',
      content: 'NFT市场在经历了前期的疯狂增长后，交易量在过去一个月内大幅下降了60%。这一现象引发了市场对NFT是否存在泡沫以及泡沫是否已经破裂的广泛讨论。',
      source: 'OpenSea',
      author: '孙七',
      published_at: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
      image_url: 'https://via.placeholder.com/400x200/7c3aed/ffffff?text=NFT+News',
      tags: ['NFT', '市场分析', '泡沫'],
      sentiment: 'negative' as const,
      related_coins: ['ethereum'],
    },
  ],

  // 模拟用户配置
  userConfig: {
    followed_coins: ['bitcoin', 'ethereum', 'solana'],
    notification_settings: {
      price_alerts: true,
      news_alerts: true,
      market_updates: false,
    },
    display_preferences: {
      currency: 'USD',
      language: 'zh-CN',
      theme: 'light',
    },
  },
};

// 模拟 API 延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 模拟 API 服务
export const mockApiService = {
  async getCoins(limit = 100) {
    await delay(500); // 模拟网络延迟
    return {
      success: true,
      data: mockData.coins.slice(0, limit),
    };
  },

  async getNews(pageSize = 20, page = 1) {
    await delay(300);
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return {
      success: true,
      data: {
        articles: mockData.news.slice(startIndex, endIndex),
        total: mockData.news.length,
        page,
        pageSize,
      },
    };
  },

  async getUserConfig() {
    await delay(200);
    return {
      success: true,
      data: mockData.userConfig,
    };
  },

  async updateUserConfig(config: Partial<typeof mockData.userConfig>) {
    await delay(300);
    Object.assign(mockData.userConfig, config);
    return {
      success: true,
      data: mockData.userConfig,
    };
  },
};
