import { create } from 'zustand';
import {
  createJSONStorage,
  persist,
  subscribeWithSelector,
} from 'zustand/middleware';
import api from '../services/api';
import type {
  AnalysisResponse,
  BaseRequest,
  CoinConfig,
  Cryptocurrency,
  TrendingTopic,
} from '../types';
import { createZustandStorage } from '../utils/storage';
import { throttle } from '../utils/timing';
import { useLoadingStore } from './loadingStore';

// 数据状态接口
interface DataState {
  // 加密货币数据
  cryptocurrencies: Cryptocurrency[];
  cryptoError: string | null;
  cryptoLastUpdated: number | null;

  // 趋势话题数据
  trendingTopics: TrendingTopic[];
  topicsError: string | null;
  topicsLastUpdated: number | null;

  // 市场分析相关
  availableCoins: CoinConfig[];
  coinsLoading: boolean;
  coinsError: string | null;
  analysisResult: AnalysisResponse | null;
  analysisLoading: boolean;
  analysisError: string | null;

  // 缓存设置
  cacheExpiry: number; // 缓存过期时间（毫秒）
}

// 数据动作接口
interface DataActions {
  // 加密货币相关
  fetchCryptocurrencies: (params?: {
    page?: number;
    limit?: number;
    forceRefresh?: boolean;
  }) => Promise<void>;
  getCryptocurrencyById: (id: string) => Cryptocurrency | undefined;
  updateCryptocurrency: (id: string, updates: Partial<Cryptocurrency>) => void;
  fetchCryptoAnalysisTrend: (data: BaseRequest) => Promise<any>;

  // 市场分析相关
  fetchAvailableCoins: () => Promise<void>;
  performAnalysis: (request: any) => Promise<void>;
  clearAnalysisResult: () => void;

  // 缓存管理
  isCacheValid: (lastUpdated: number | null) => boolean;
  clearCache: (type?: 'crypto' | 'topics') => void;
  clearAllCache: () => void;

  // 错误处理
  clearErrors: () => void;
  clearError: (type: 'crypto' | 'topics' | 'analysis') => void;
}

// 加载状态 ID 常量
const LOADING_IDS = {
  CRYPTO_LIST: 'crypto-list',
  CRYPTO_TRENDING: 'crypto-trending',
  TOPICS_TRENDING: 'topics-trending',
} as const;

// 创建数据store
export const useDataStore = create<DataState & DataActions>()(
  subscribeWithSelector(
    persist(
      (set, get) => ({
        // 初始状态
        cryptocurrencies: [],
        trendingCryptos: [],
        cryptoError: null,
        cryptoLastUpdated: null,

        // 删除新闻初始状态
        // 删除搜索初始状态

        trendingTopics: [],
        topicsError: null,
        topicsLastUpdated: null,

        // 删除searchResults、searchKeyword、searchLastUpdated

        // 市场分析初始状态
        availableCoins: [],
        coinsLoading: false,
        coinsError: null,
        analysisResult: null,
        analysisLoading: false,
        analysisError: null,

        cacheExpiry: 5 * 60 * 1000, // 5分钟缓存

        // 缓存管理
        isCacheValid: (lastUpdated: number | null) => {
          if (!lastUpdated) return false;
          const { cacheExpiry } = get();
          return Date.now() - lastUpdated < cacheExpiry;
        },

        clearCache: type => {
          if (!type) {
            set({
              cryptocurrencies: [],
              cryptoLastUpdated: null,
              // 移除新闻与搜索字段
              trendingTopics: [],
              topicsLastUpdated: null,
            });
            return;
          }

          switch (type) {
            case 'crypto':
              set({
                cryptocurrencies: [],
                cryptoLastUpdated: null,
              });
              break;
            case 'topics':
              set({
                trendingTopics: [],
                topicsLastUpdated: null,
              });
              break;
          }
        },

        clearAllCache: () => {
          get().clearCache();
        },

        // 加密货币分析趋势
        fetchCryptoAnalysisTrend: async (data: BaseRequest) => {
          const { coins } = data;
          if (!coins) return;

          const coinList = (
            Array.isArray(coins) ? coins.join(',') : (coins as string)
          )
            .split(',')
            .map((coin: string) => coin.toUpperCase());

          return api.crypto.getAnalysisTrend(data);
        },

        // 加密货币相关方法
        fetchCryptocurrencies: throttle(async (params = {}) => {
          const { forceRefresh = false } = params;
          const { cryptoLastUpdated, isCacheValid } = get();

          // 检查缓存
          if (!forceRefresh && isCacheValid(cryptoLastUpdated)) {
            return;
          }

          const loadingStore = useLoadingStore.getState();
          loadingStore.startLoading(
            LOADING_IDS.CRYPTO_LIST,
            '获取加密货币列表...'
          );

          try {
            set({ cryptoError: null });

            // 模拟进度更新
            loadingStore.updateProgress(LOADING_IDS.CRYPTO_LIST, 30);

            const response = await api.crypto.getList(params);
            const cryptocurrencies = response.data || [];

            loadingStore.updateProgress(LOADING_IDS.CRYPTO_LIST, 80);

            set({
              cryptocurrencies: (
                cryptocurrencies as unknown as CoinConfig[]
              ).map((crypto: any) => ({
                ...crypto,
                rank: 0,
                supply: {
                  circulating: 0,
                  total: 0,
                  max: 0,
                },
                tags: [],
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
              })),
              cryptoLastUpdated: Date.now(),
              cryptoError: null,
            });

            loadingStore.finishLoading(LOADING_IDS.CRYPTO_LIST, 'success');
          } catch (error: any) {
            set({
              cryptoError: error.message || '获取加密货币数据失败',
            });
            loadingStore.finishLoading(LOADING_IDS.CRYPTO_LIST, 'error');
            throw error;
          }
        }),

        getCryptocurrencyById: (id: string) => {
          const { cryptocurrencies } = get();
          return [...cryptocurrencies].find(crypto => crypto.id === id);
        },

        updateCryptocurrency: (
          id: string,
          updates: Partial<Cryptocurrency>
        ) => {
          set(state => ({
            cryptocurrencies: state.cryptocurrencies.map(crypto =>
              crypto.id === id ? { ...crypto, ...updates } : crypto
            ),
          }));
        },

        // 市场分析相关方法
        fetchAvailableCoins: async () => {
          const { startLoading, clearLoading } = useLoadingStore.getState();

          try {
            set({ coinsLoading: true, coinsError: null });
            startLoading('fetch-coins', '获取币种列表中...');

            const response = await api.crypto.getList({ limit: 100 });
            const payload = response.data;
            const list = Array.isArray(payload?.data)
              ? (payload.data as CoinConfig[])
              : [];
            if (payload?.success === true && list.length > 0) {
              set({
                availableCoins: list,
                coinsLoading: false,
              });
            } else {
              throw new Error('获取币种列表失败');
            }
          } catch (error) {
            console.error('获取币种列表失败:', error);
            set({
              coinsError:
                error instanceof Error ? error.message : '获取币种列表失败',
              coinsLoading: false,
            });
          } finally {
            clearLoading('fetch-coins');
          }
        },

        performAnalysis: async (request: any) => {
          const { startLoading, clearLoading } = useLoadingStore.getState();

          try {
            set({
              analysisLoading: true,
              analysisError: null,
              analysisResult: null,
            });
            startLoading('analysis', 'AI 正在分析中，请稍候...');

            const response = await api.crypto.analyze(request);
            const analysisData = response.data;

            if (analysisData.success === true) {
              set({
                analysisResult: analysisData,
                analysisLoading: false,
              });
            } else {
              throw new Error(analysisData.message || '分析失败');
            }
          } catch (error) {
            console.error('分析失败:', error);
            set({
              analysisError:
                error instanceof Error ? error.message : '分析失败',
              analysisLoading: false,
            });
          } finally {
            clearLoading('analysis');
          }
        },

        clearAnalysisResult: () => {
          set({
            analysisResult: null,
            analysisError: null,
          });
        },

        // 错误处理
        clearErrors: () => {
          set({
            cryptoError: null,
            topicsError: null,
            coinsError: null,
            analysisError: null,
          });
        },

        clearError: (type: 'crypto' | 'topics' | 'analysis') => {
          switch (type) {
            case 'crypto':
              set({ cryptoError: null });
              break;
            case 'topics':
              set({ topicsError: null });
              break;
            case 'analysis':
              set({ analysisError: null, coinsError: null });
              break;
          }
        },
      }),
      {
        name: 'data-storage',
        storage: createJSONStorage(() => createZustandStorage()),
        // 选择性持久化，只保存数据，不保存错误状态
        partialize: state => ({
          cryptocurrencies: state.cryptocurrencies,
          cryptoLastUpdated: state.cryptoLastUpdated,
          // 移除新闻相关字段
          trendingTopics: state.trendingTopics,
          topicsLastUpdated: state.topicsLastUpdated,
          availableCoins: state.availableCoins,
          cacheExpiry: state.cacheExpiry,
        }),
      }
    )
  )
);

// 选择器函数，用于优化组件订阅
export const dataSelectors = {
  // 加密货币选择器
  getCryptocurrencies: () => (state: DataState & DataActions) =>
    state.cryptocurrencies,
  getCryptoError: () => (state: DataState & DataActions) => state.cryptoError,
  getCryptoLastUpdated: () => (state: DataState & DataActions) =>
    state.cryptoLastUpdated,

  // 话题选择器
  getTrendingTopics: () => (state: DataState & DataActions) =>
    state.trendingTopics,
  getTopicsError: () => (state: DataState & DataActions) => state.topicsError,
  getTopicsLastUpdated: () => (state: DataState & DataActions) =>
    state.topicsLastUpdated,

  // 市场分析选择器
  getAvailableCoins: () => (state: DataState & DataActions) =>
    state.availableCoins,
  getCoinsLoading: () => (state: DataState & DataActions) => state.coinsLoading,
  getCoinsError: () => (state: DataState & DataActions) => state.coinsError,
  getAnalysisResult: () => (state: DataState & DataActions) =>
    state.analysisResult,
  getAnalysisLoading: () => (state: DataState & DataActions) =>
    state.analysisLoading,
  getAnalysisError: () => (state: DataState & DataActions) =>
    state.analysisError,

  // 通用选择器
  hasAnyError: () => (state: DataState & DataActions) =>
    !!(
      state.cryptoError ||
      state.topicsError ||
      state.coinsError ||
      state.analysisError
    ),
};

// 导出加载状态 ID，供组件使用
export { LOADING_IDS };

// 初始化数据
export const initDataStore = () => {
  const { fetchCryptocurrencies } = useDataStore.getState();

  // 获取初始数据
  Promise.all([fetchCryptocurrencies()]).catch(error => {
    console.error('Failed to init data store:', error);
  });
};
