import { create } from 'zustand';
import {
  createJSONStorage,
  persist,
  subscribeWithSelector,
} from 'zustand/middleware';
import api from '../../services/api';
import { ResponseData } from '../../services/request';
import type { NewsItemType } from '../../types';
import { NewsListResponse } from '../../types/newsType';
import { createZustandStorage } from '../../utils/storage';
import { throttle } from '../../utils/timing';
import { useLoadingStore } from '../loadingStore';

// 新闻分类类型 - 移除trending，简化为query-based分类
export type NewsCategory = 'bitcoin' | 'ethereum' | 'defi' | 'nft';

// 新闻数据状态接口
interface NewsState {
  // 新闻数据 - 简化结构
  news: NewsItemType[];
  categorizedNews: Record<NewsCategory, NewsItemType[]>;
  newsError: string | null;
  newsLastUpdated: number | null;
  newsLoading: boolean;
  currentCategory: NewsCategory;

  // 缓存设置
  cacheExpiry: number; // 缓存过期时间（毫秒）
}

// 新闻数据动作接口
interface NewsActions {
  // 新闻相关
  fetchNewsByCategory: (
    category: NewsCategory,
    forceRefresh?: boolean
  ) => Promise<void>;
  setCurrentCategory: (category: NewsCategory) => void;
  fetchNewsDetail: (url: string) => Promise<NewsItemType>;

  // 缓存管理
  isCacheValid: (lastUpdated: number | null) => boolean;
  clearCache: () => void;

  // 错误处理
  clearError: () => void;
}

// 加载状态 ID 常量 - 移除趋势和搜索
const NEWS_LOADING_IDS = {
  NEWS_LIST: 'news-list',
  NEWS_CATEGORY: 'news-category',
  NEWS_DETAIL: 'news-detail',
} as const;

// 新闻分类配置 - 简化配置
const NEWS_CATEGORIES: Record<NewsCategory, { name: string; query?: string }> =
  {
    bitcoin: { name: 'Bitcoin', query: 'bitcoin' },
    ethereum: { name: 'Ethereum', query: 'ethereum' },
    defi: { name: 'DeFi', query: 'defi' },
    nft: { name: 'NFT', query: 'nft' },
  };

// 创建新闻store
export const useNewsStore = create<NewsState & NewsActions>()(
  subscribeWithSelector(
    persist(
      (set, get) => ({
        // 初始状态
        news: [],
        categorizedNews: {
          bitcoin: [],
          ethereum: [],
          defi: [],
          nft: [],
        },
        newsError: null,
        newsLastUpdated: null,
        newsLoading: false,
        currentCategory: 'bitcoin',

        cacheExpiry: 5 * 60 * 1000, // 5分钟缓存

        // 缓存管理
        isCacheValid: (lastUpdated: number | null) => {
          if (!lastUpdated) return false;
          const { cacheExpiry } = get();
          return Date.now() - lastUpdated < cacheExpiry;
        },

        clearCache: () => {
          set({
            news: [],
            categorizedNews: {
              bitcoin: [],
              ethereum: [],
              defi: [],
              nft: [],
            },
            newsLastUpdated: null,
            newsLoading: false,
          });
        },

        fetchNewsByCategory: throttle(
          async (category: NewsCategory, forceRefresh = false) => {
            const { categorizedNews, newsLastUpdated, isCacheValid } = get();

            // 检查缓存
            if (
              !forceRefresh &&
              categorizedNews[category].length > 0 &&
              isCacheValid(newsLastUpdated)
            ) {
              return;
            }

            const loadingStore = useLoadingStore.getState();
            loadingStore.startLoading(
              NEWS_LOADING_IDS.NEWS_CATEGORY,
              `获取${NEWS_CATEGORIES[category].name}新闻...`
            );

            try {
              set({ newsError: null, newsLoading: true });

              // 特定分类的新闻 - 使用query参数
              const categoryQuery = NEWS_CATEGORIES[category].query;
              const response: ResponseData<NewsListResponse> =
                await api.news.getList({
                  page_size: 20,
                  query: categoryQuery as string,
                  page: 1,
                  sort_by: 'publishedAt',
                  language: 'zh',
                });

              const listPayload = response.data;
              const processedNews = (listPayload['articles'] || []).map(
                item => ({
                  id: item.id,
                  title: item.title,
                  content: item.content,
                  url: item.url,
                  imageUrl: '',
                  source: item.source,
                  author: undefined,
                  publishedAt: item.published_at,
                  relatedCoins: [],
                  createdAt: item.published_at,
                  updatedAt: item.fetched_at || item.published_at,
                })
              );

              set(state => ({
                newsLoading: true,
                categorizedNews: {
                  ...state.categorizedNews,
                  [category]: processedNews,
                },
                newsLastUpdated: Date.now(),
                newsError: null,
              }));

              loadingStore.updateProgress(NEWS_LOADING_IDS.NEWS_CATEGORY, 80);

              loadingStore.finishLoading(
                NEWS_LOADING_IDS.NEWS_CATEGORY,
                'success'
              );
            } catch (error: any) {
              set({
                newsError:
                  error.message ||
                  `获取${NEWS_CATEGORIES[category].name}新闻失败`,
                newsLoading: false,
              });
              loadingStore.finishLoading(
                NEWS_LOADING_IDS.NEWS_CATEGORY,
                'error'
              );
              throw error;
            }
          }
        ),

        setCurrentCategory: (category: NewsCategory) => {
          set({ currentCategory: category });
        },
        // 新增新闻详情获取方法
        fetchNewsDetail: async (url: string) => {
          const loadingStore = useLoadingStore.getState();
          loadingStore.startLoading(
            NEWS_LOADING_IDS.NEWS_DETAIL,
            '获取新闻详情...'
          );

          try {
            const response = await api.news.getDetail(url);
            const newsDetail = response.data;

            // 补充BaseEntity需要的字段，以符合types中NewsItemType接口
            const processedDetail: NewsItemType = {
              id: newsDetail.id,
              title: newsDetail.title,
              description: newsDetail.description,
              content: newsDetail.content,
              url: newsDetail.url,
              imageUrl: '',
              source: newsDetail.source,
              author: undefined,
              publishedAt: newsDetail.published_at,
              relatedCoins: [],
              createdAt: newsDetail.published_at,
              updatedAt: newsDetail.fetched_at || newsDetail.published_at,
              tags: [],
              sentiment: 'neutral',
              views: 0,
              likes: 0,
              shares: 0,
              comments: 0,
            };

            loadingStore.finishLoading(NEWS_LOADING_IDS.NEWS_DETAIL, 'success');
            return processedDetail;
          } catch (error: any) {
            loadingStore.finishLoading(NEWS_LOADING_IDS.NEWS_DETAIL, 'error');
            throw error;
          }
        },

        // 错误处理
        clearError: () => {
          set({
            newsError: null,
          });
        },
      }),
      {
        name: 'news-storage',
        storage: createJSONStorage(() => createZustandStorage()),
        // 选择性持久化，只保存数据，不保存错误状态
        partialize: state => ({
          news: state.news,
          categorizedNews: state.categorizedNews,
          newsLastUpdated: state.newsLastUpdated,
          currentCategory: state.currentCategory,
          cacheExpiry: state.cacheExpiry,
        }),
      }
    )
  )
);

// 选择器函数，用于优化组件订阅 - 移除trends和search相关
export const newsSelectors = {
  // 新闻选择器
  getCategorizedNews:
    (category: NewsCategory) => (state: NewsState & NewsActions) =>
      state.categorizedNews[category],
  getCurrentCategoryNews: () => (state: NewsState & NewsActions) =>
    state.categorizedNews[state.currentCategory],
  getNewsError: () => (state: NewsState & NewsActions) => state.newsError,
  getNewsLastUpdated: () => (state: NewsState & NewsActions) =>
    state.newsLastUpdated,
  getNewsLoading: () => (state: NewsState & NewsActions) => state.newsLoading,
  getCurrentCategory: () => (state: NewsState & NewsActions) =>
    state.currentCategory,
};

// 导出加载状态 ID 和分类配置
export { NEWS_CATEGORIES, NEWS_LOADING_IDS };

// 初始化新闻数据 - 简化为基础新闻获取
export const initNewsStore = () => {};
