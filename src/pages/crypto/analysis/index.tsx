import React, { useState, useEffect } from 'react';
import { View, Text } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { Button, Input, Checkbox, Toast, Loading } from '@nutui/nutui-react-taro';
import api, { AnalysisRequest, CoinAnalysisResult } from '../../../services/api';
import './index.scss';

interface AnalysisPageState {
  availableCoins: Array<{ id: string; name: string; symbol: string }>;
  selectedCoins: string[];
  analysisResults: CoinAnalysisResult[];
  loading: boolean;
  analyzing: boolean;
  error: string | null;
}

const AnalysisPage: React.FC = () => {
  const [state, setState] = useState<AnalysisPageState>({
    availableCoins: [],
    selectedCoins: [],
    analysisResults: [],
    loading: true,
    analyzing: false,
    error: null,
  });

  // 加载可用币种
  useEffect(() => {
    loadAvailableCoins();
  }, []);

  const loadAvailableCoins = async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      
      const response = await api.crypto.getList();
      if (response.data?.success && response.data?.data) {
        setState(prev => ({
          ...prev,
          availableCoins: response.data.data,
          loading: false,
        }));
      } else {
        throw new Error('获取币种列表失败');
      }
    } catch (error) {
      console.error('加载币种失败:', error);
      setState(prev => ({
        ...prev,
        error: '加载币种列表失败',
        loading: false,
      }));
      
      Taro.showToast({
        title: '加载币种列表失败',
        icon: 'error',
      });
    }
  };

  // 选择/取消选择币种
  const toggleCoinSelection = (coinId: string) => {
    setState(prev => ({
      ...prev,
      selectedCoins: prev.selectedCoins.includes(coinId)
        ? prev.selectedCoins.filter(id => id !== coinId)
        : [...prev.selectedCoins, coinId],
    }));
  };

  // 执行分析
  const performAnalysis = async () => {
    if (state.selectedCoins.length === 0) {
      Taro.showToast({
        title: '请选择至少一个币种',
        icon: 'none',
      });
      return;
    }

    if (state.selectedCoins.length > 10) {
      Taro.showToast({
        title: '最多只能选择10个币种',
        icon: 'none',
      });
      return;
    }

    try {
      setState(prev => ({ ...prev, analyzing: true, error: null }));

      const analysisRequest: AnalysisRequest = {
        coins: state.selectedCoins,
        model: 'deepseek-chat',
        provider: 'deepseek',
        news_days: 7,
      };

      const response = await api.crypto.analyze(analysisRequest);
      
      if (response.data?.success) {
        setState(prev => ({
          ...prev,
          analysisResults: response.data.results,
          analyzing: false,
        }));
        
        Taro.showToast({
          title: `成功分析 ${response.data.successful_analyses}/${response.data.total_coins} 个币种`,
          icon: 'success',
        });
      } else {
        throw new Error(response.data?.message || '分析失败');
      }
    } catch (error) {
      console.error('分析失败:', error);
      setState(prev => ({
        ...prev,
        error: '分析失败，请稍后重试',
        analyzing: false,
      }));
      
      Taro.showToast({
        title: '分析失败',
        icon: 'error',
      });
    }
  };

  // 渲染分析结果
  const renderAnalysisResult = (result: CoinAnalysisResult) => (
    <View key={result.coin_id} className="analysis-result">
      <View className="result-header">
        <Text className="coin-name">{result.coin_name} ({result.coin_symbol})</Text>
        <Text className={`status ${result.success ? 'success' : 'error'}`}>
          {result.success ? '分析成功' : '分析失败'}
        </Text>
      </View>
      
      {result.success ? (
        <View className="result-content">
          {result.current_price && (
            <View className="price-info">
              <Text>当前价格: ${result.current_price}</Text>
              {result.price_change_24h && (
                <Text className={result.price_change_24h >= 0 ? 'positive' : 'negative'}>
                  24h变化: {result.price_change_24h >= 0 ? '+' : ''}{result.price_change_24h}%
                </Text>
              )}
            </View>
          )}
          
          <View className="news-info">
            <Text>新闻数量: {result.news_count}</Text>
          </View>
          
          {result.overall_sentiment && (
            <View className="sentiment">
              <Text>整体情绪: {result.overall_sentiment}</Text>
              {result.overall_sentiment_score && (
                <Text>情绪分数: {result.overall_sentiment_score}</Text>
              )}
            </View>
          )}
          
          {result.ai_analysis && (
            <View className="ai-analysis">
              <Text className="section-title">AI分析:</Text>
              <Text className="analysis-text">{result.ai_analysis}</Text>
            </View>
          )}
          
          {result.strategy && (
            <View className="strategy">
              <Text className="section-title">投资策略:</Text>
              <Text>{result.strategy}</Text>
            </View>
          )}
          
          {result.risk_warning && (
            <View className="risk-warning">
              <Text className="section-title">风险提示:</Text>
              <Text className="warning-text">{result.risk_warning}</Text>
            </View>
          )}
        </View>
      ) : (
        <View className="error-content">
          <Text className="error-message">{result.error_message}</Text>
        </View>
      )}
    </View>
  );

  if (state.loading) {
    return (
      <View className="analysis-page">
        <Loading />
        <Text>加载币种列表中...</Text>
      </View>
    );
  }

  return (
    <View className="analysis-page">
      <View className="page-header">
        <Text className="page-title">币种分析</Text>
        <Text className="page-subtitle">选择币种进行AI智能分析</Text>
      </View>

      {state.error && (
        <View className="error-banner">
          <Text>{state.error}</Text>
        </View>
      )}

      <View className="coin-selection">
        <View className="selection-header">
          <Text className="section-title">选择币种 ({state.selectedCoins.length}/10)</Text>
          <Button
            size="small"
            type="primary"
            onClick={() => setState(prev => ({ ...prev, selectedCoins: [] }))}
            disabled={state.selectedCoins.length === 0}
          >
            清空选择
          </Button>
        </View>

        <View className="coin-list">
          {state.availableCoins.map(coin => (
            <View key={coin.id} className="coin-item">
              <Checkbox
                checked={state.selectedCoins.includes(coin.id)}
                onChange={() => toggleCoinSelection(coin.id)}
              >
                <Text className="coin-info">
                  {coin.name} ({coin.symbol})
                </Text>
              </Checkbox>
            </View>
          ))}
        </View>
      </View>

      <View className="action-section">
        <Button
          type="primary"
          size="large"
          loading={state.analyzing}
          onClick={performAnalysis}
          disabled={state.selectedCoins.length === 0 || state.analyzing}
        >
          {state.analyzing ? '分析中...' : '开始分析'}
        </Button>
      </View>

      {state.analysisResults.length > 0 && (
        <View className="results-section">
          <Text className="section-title">分析结果</Text>
          {state.analysisResults.map(renderAnalysisResult)}
        </View>
      )}
    </View>
  );
};

export default AnalysisPage;
