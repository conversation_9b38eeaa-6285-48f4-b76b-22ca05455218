.news-page {
  min-height: 100vh;
  background: var(--color-background);
  color: var(--color-text-primary);
  padding: 0.3rem;
  padding-bottom: 5rem; // 为TabBar留出足够空间，避免遮挡
}

.nav-container {
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--color-border);
  .nav-left {
    align-items: flex-start;
  }
  .page-header {
    justify-items: flex-start;
  }
}

/* 新增：分类Tabs区域样式 */
.news-categories {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.category-tabs {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  overflow-x: auto;
  padding-bottom: 0.25rem;
}

.category-tab {
  padding: 0.4rem 0.6rem;
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 9999px;
  white-space: nowrap;
  color: var(--color-text-secondary);
  transition: all 0.2s ease-in-out;
}

.category-tab:active {
  transform: scale(0.98);
}

.category-tab:hover {
  border-color: var(--color-primary);
}

.category-tab.active {
  background: var(--color-primary);
  color: #fff;
  border-color: var(--color-primary);
}

.category-name {
  font-size: 0.75rem;
  font-weight: 500;
}

.news-list {
  margin-top: 1rem;
  .news-item {
    padding: 1rem;
    background: var(--color-surface);
    border-radius: 8px;
    border: 1px solid var(--color-border);
    margin-bottom: 0.5rem;
    transition: all 0.2s ease-in-out;

    &:hover {
      transform: translateY(-1px);
      box-shadow: var(--shadow-sm);
    }

    .news-content {
      .news-title {
        width: 16rem;
        font-size: 0.8rem;
        font-weight: 600;
        color: var(--color-text-primary);
        margin-bottom: 0.5rem;
        display: block;
        line-height: 1.4;
      }

      .news-summary {
        width: 16rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: wrap;
        display: -webkit-box;
        line-clamp: 2;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        font-size: 0.55rem;
        color: var(--color-text-secondary);
        margin-bottom: 0.5rem;
        display: block;
        line-height: 1.5;
      }

      .news-meta {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .news-source {
          font-size: 0.75rem;
          color: var(--color-primary);
          font-weight: 500;
        }

        .news-date {
          font-size: 0.75rem;
          color: var(--color-text-secondary);
        }
      }
    }

    .news-image {
      width: 100%;
      border-radius: 4px;
      overflow: hidden;
    }
  }
}

/* 新增：错误视图样式 */
.news-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 1rem 0;

  .error-message {
    color: var(--color-text-secondary);
    font-size: 0.85rem;
  }
}

.load-more {
  padding: 1rem 0;
  text-align: center;
}

// 响应式样式
@media (max-width: 640px) {
  .page-header {
    padding: 1rem 0;
  }

  .news-list {
    .news-item {
      padding: 0.75rem;

      .news-content {
        .news-title {
          font-size: 0.9rem;
        }

        .news-summary {
          font-size: 0.8rem;
        }
      }
    }
  }
}

// 小程序端特殊样式
.platform-weapp,
.platform-alipay,
.platform-swan,
.platform-tt,
.platform-qq,
.platform-jd {
  .news-page {
    padding-bottom: 100rpx;
  }

  .news-item {
    &:active {
      opacity: 0.8;
      transform: scale(0.98);
    }
  }
}

// H5端特殊样式
.platform-h5 {
  .news-item {
    cursor: pointer;

    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
    }
  }
}
