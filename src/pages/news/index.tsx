import { Button } from '@nutui/nutui-react-taro';
import { ScrollView, Text, View } from '@tarojs/components';
import React, { useEffect, useState } from 'react';
import Container from '../../components/Layout/Container';
import Grid from '../../components/Layout/Grid';
import LazyImage from '../../components/LazyLoad/LazyImage';
import { NavigationBar } from '../../components/Navigation';
import { SkeletonListItem } from '../../components/Skeleton/Skeleton';
import { loadingSelectors, useLoadingStore } from '../../stores/loadingStore';
import {
  NEWS_CATEGORIES,
  NEWS_LOADING_IDS,
  NewsCategory,
  newsSelectors,
  useNewsStore,
} from '../../stores/news-fetch';
import { navigation } from '../../utils/platform';
import './index.scss';

const News: React.FC = () => {
  const currentCategory = useNewsStore(newsSelectors.getCurrentCategory());
  const currentNews = useNewsStore(newsSelectors.getCurrentCategoryNews());
  const newsError = useNewsStore(newsSelectors.getNewsError());

  const fetchNewsByCategory = useNewsStore(state => state.fetchNewsByCategory);
  const setCurrentCategory = useNewsStore(state => state.setCurrentCategory);
  const clearError = useNewsStore(state => state.clearError);

  // 使用专门的新闻加载状态
  const newsLoading = useLoadingStore(
    loadingSelectors.getLoadingById(NEWS_LOADING_IDS.NEWS_CATEGORY)
  );
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    let isSubscribed = true;

    const loadNews = async () => {
      try {
        await fetchNewsByCategory(currentCategory);
      } catch (error) {
        if (isSubscribed) {
          console.error('获取新闻失败:', error);
        }
      }
    };

    loadNews();

    return () => {
      isSubscribed = false;
    };
  }, [currentCategory, fetchNewsByCategory]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await fetchNewsByCategory(currentCategory, true);
    } finally {
      setRefreshing(false);
    }
  };

  const handleCategoryChange = (category: NewsCategory) => {
    if (category === currentCategory) return;

    setCurrentCategory(category);
  };

  const handleLoadMore = async () => {
    try {
      await fetchNewsByCategory(currentCategory, true);
    } catch (error) {
      console.error('加载更多失败:', error);
    }
  };
  // 跳转到新闻详情页
  const navigateToNewsDetail = (url: string) => {
    navigation.navigateTo(
      `/pages/news-details/index?url=${encodeURIComponent(url)}`
    );
  };

  // 错误处理
  if (newsError) {
    return (
      <>
        <NavigationBar
          title='📰 加密货币资讯'
          showBack={true}
          showThemeToggle={false}
        />
        <Container maxWidth='xl' padding>
          <View className='news-error'>
            <Text className='error-message'>{newsError}</Text>
            <Button
              type='primary'
              size='small'
              onClick={() => {
                clearError();
                fetchNewsByCategory(currentCategory, true);
              }}
            >
              重试
            </Button>
          </View>
        </Container>
      </>
    );
  }

  return (
    <>
      <NavigationBar
        title='📰 加密货币资讯'
        showBack={true}
        showThemeToggle={false}
      />
      <ScrollView
        className='news-page'
        scrollY
        refresherEnabled
        refresherTriggered={refreshing}
        onRefresherRefresh={handleRefresh}
      >
        <Container maxWidth='xl' padding>
          {/* 分类选择 Tabs */}
          <View className='news-categories'>
            <View className='category-tabs'>
              {Object.entries(NEWS_CATEGORIES).map(([key, config]) => (
                <View
                  key={key}
                  className={`category-tab ${
                    currentCategory === key ? 'active' : ''
                  }`}
                  onClick={() => handleCategoryChange(key as NewsCategory)}
                >
                  <Text className='category-name'>{config.name}</Text>
                </View>
              ))}
            </View>
          </View>

          {/* 新闻列表 */}
          <View className='news-list'>
            {newsLoading?.type === 'loading' && currentNews.length === 0
              ? Array.from({ length: 6 }).map((_, index) => (
                  <View key={index} className='news-item'>
                    <SkeletonListItem showAvatar showSecondary />
                  </View>
                ))
              : currentNews.map(newsItem => (
                  <View
                    key={newsItem.id}
                    className='news-item'
                    onClick={() => navigateToNewsDetail(newsItem.url)}
                  >
                    <Grid container spacing={2}>
                      <Grid item xs={8}>
                        <View className='news-content'>
                          <Text className='news-title'>{newsItem.title}</Text>
                          <Text className='news-summary'>
                            {newsItem.description}
                          </Text>
                          <View className='news-meta'>
                            <Text className='news-source'>
                              {newsItem.source}
                            </Text>
                            <Text className='news-date'>
                              {new Date(
                                newsItem.publishedAt
                              ).toLocaleDateString()}
                            </Text>
                          </View>
                        </View>
                      </Grid>
                      <Grid item xs={4}>
                        <LazyImage
                          src={newsItem.imageUrl}
                          alt={newsItem.title}
                          aspectRatio={16 / 9}
                          className='news-image'
                        />
                      </Grid>
                    </Grid>
                  </View>
                ))}
          </View>

          {/* 加载更多 */}
          {currentNews.length > 0 && (
            <View className='load-more'>
              <Button
                type='info'
                size='small'
                loading={newsLoading?.type === 'loading'}
                onClick={handleLoadMore}
              >
                加载更多
              </Button>
            </View>
          )}
        </Container>
      </ScrollView>
    </>
  );
};

export default News;
