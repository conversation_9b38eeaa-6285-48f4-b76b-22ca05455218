import { ScrollView, Text, View } from '@tarojs/components';
import React, { useEffect, useState } from 'react';
import Container from '../../components/Layout/Container';
import Grid from '../../components/Layout/Grid';
import ResponsiveText from '../../components/Layout/ResponsiveText';
import LazyImage from '../../components/LazyLoad/LazyImage';
import {
  GlobalLoadingOverlay,
  InlineLoading,
} from '../../components/LoadingIndicator';
import { NavigationBar } from '../../components/Navigation';
import {
  CryptoCardSkeleton,
  NewsCardSkeleton,
} from '../../components/Skeleton';
import TabBar from '../../components/TabBar';
import { dataSelectors, useDataStore } from '../../stores/dataStore';
import { loadingSelectors, useLoadingStore } from '../../stores/loadingStore';
import {
  NEWS_LOADING_IDS,
  newsSelectors,
  useNewsStore,
} from '../../stores/news-fetch';
import { BaseRequest } from '../../types';
import { navigation } from '../../utils/platform';
import './index.scss';

const Index: React.FC = () => {
  // 使用选择器优化订阅
  const newsList = useNewsStore(newsSelectors.getCategorizedNews('bitcoin'));
  const cryptocurrencies = useDataStore(dataSelectors.getCryptocurrencies());
  const { fetchCryptocurrencies, fetchCryptoAnalysisTrend } = useDataStore();

  // 使用加载状态管理
  const cryptoLoading = useLoadingStore(
    loadingSelectors.getLoadingById('crypto')
  );
  const newsLoading = useLoadingStore(
    loadingSelectors.getLoadingById(NEWS_LOADING_IDS.NEWS_LIST)
  );

  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    // 初始化数据加载
    const initializeData = async () => {
      try {
        await Promise.all([fetchCryptocurrencies()]);
      } catch (error) {
        console.error('数据初始化失败:', error);
      }
    };
    initializeData();
  }, [fetchCryptocurrencies]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([fetchCryptocurrencies()]);
    } finally {
      setRefreshing(false);
    }
  };

  // 跳转news页面
  const navigateToNews = () => {
    navigation.navigateTo('/pages/news/index');
  };

  // 跳转市场分析页面
  // const navigateToMarketAnalysis = () => {
  //   navigation.navigateTo('/pages/market-analysis/index');
  // };

  // 跳转到测试Demo页面
  // const navigateToTestDemo = () => {
  //   navigation.navigateTo('/pages/test-demo/index');
  // };

  // 获取分析结果
  const handleAnalyzeTrend = async () => {
    try {
      const data: BaseRequest = {
        coins: ['pepe'],
        model: 'deepseek-chat',
        provider: 'deepseek',
        news_days: 1,
      };
      await fetchCryptoAnalysisTrend(data);
    } catch (error) {
      console.error('分析失败:', error);
    }
  };

  return (
    <>
      <GlobalLoadingOverlay showProgress showMessage backdrop />
      <NavigationBar
        title='加密货币资讯'
        showBack={false}
        showThemeToggle={true}
      />
      <ScrollView
        className='index-page'
        scrollY
        refresherEnabled
        refresherTriggered={refreshing}
        onRefresherRefresh={handleRefresh}
      >
        <Container maxWidth='xl' padding>
          {/* 功能卡片区域 */}
          <View className='feature-cards'>
            <Grid container spacing={2}>
              {/* <Grid item xs={12} sm={4}>
                <View className='feature-card' onClick={navigateToCrypto}>
                  <Text className='feature-title'>📈 实时行情</Text>
                  <Text className='feature-desc'>
                    查看最新的加密货币价格和市场趋势
                  </Text>
                </View>
              </Grid> */}
              <Grid item xs={12} sm={4}>
                <View className='feature-card' onClick={navigateToNews}>
                  <Text className='feature-title'>📰 热门资讯</Text>
                  <Text className='feature-desc'>
                    获取最新的区块链和加密货币新闻
                  </Text>
                </View>
              </Grid>
            </Grid>
          </View>

          {/* 热门加密货币 */}
          <View className='trending-crypto'>
            <View className='section-header'>
              <ResponsiveText
                variant='h5'
                weight='semibold'
                className='section-title'
              >
                热门加密货币
              </ResponsiveText>
              {cryptoLoading && (
                <InlineLoading
                  loadingId='crypto'
                  text='加载中...'
                  size='small'
                />
              )}
            </View>

            {cryptoLoading ? (
              <CryptoCardSkeleton count={3} />
            ) : (
              <Grid container spacing={4} justify={'center'}>
                {cryptocurrencies.map(crypto => {
                  return (
                    <Grid item xs={12} sm={6} md={4} key={crypto.id}>
                      <View className='crypto-card'>
                        <View className='crypto-header'>
                          <LazyImage
                            src={crypto.imageUrl || ''}
                            alt={crypto.name}
                            width={32}
                            height={32}
                            className='crypto-icon'
                          />
                          <View className='crypto-info'>
                            <Text className='crypto-symbol'>
                              {crypto.symbol.toUpperCase()}
                            </Text>
                            <Text className='crypto-name'>{crypto.name}</Text>
                          </View>
                        </View>
                      </View>
                    </Grid>
                  );
                })}
              </Grid>
            )}
          </View>

          {/* 热门资讯 */}
          <View className='trending-news'>
            <View className='section-header'>
              <ResponsiveText
                variant='h5'
                weight='semibold'
                className='section-title'
              >
                热门资讯
              </ResponsiveText>
              {newsLoading && (
                <InlineLoading
                  loadingId={NEWS_LOADING_IDS.NEWS_LIST}
                  text='加载中...'
                  size='small'
                />
              )}
            </View>

            {newsLoading ? (
              <NewsCardSkeleton count={3} />
            ) : (
              <Grid container spacing={2}>
                {newsList.map(news => (
                  <Grid item xs={12} key={news.id}>
                    <View className='news-card'>
                      <Grid container spacing={2}>
                        <Grid item xs={8}>
                          <Text className='news-title'>{news.title}</Text>
                          <Text className='news-summary'>
                            {news.description}
                          </Text>
                          <Text className='news-meta'>
                            {news.source} ·{' '}
                            {new Date(news.publishedAt).toLocaleDateString()}
                          </Text>
                        </Grid>
                        <Grid item xs={4}>
                          <LazyImage
                            src={news.imageUrl || ''}
                            alt={news.title}
                            aspectRatio={16 / 9}
                            className='news-image'
                          />
                        </Grid>
                      </Grid>
                    </View>
                  </Grid>
                ))}
              </Grid>
            )}
          </View>
        </Container>
      </ScrollView>

      {/* 底部导航 */}
      <TabBar current={0} />
    </>
  );
};

export default Index;
