.index-page {
  min-height: 100vh;
  background: var(--color-background);
  color: var(--color-text-primary);
  padding-bottom: 5rem;
}

.header-section {
  padding: 1rem 0;
  margin-bottom: 1.5rem;

  .theme-toggle {
    display: flex;
    justify-content: flex-end;
  }
}

.section-header {
  padding: 0 0.6rem;
}

.feature-cards {
  margin: 1rem 2rem;

  .feature-card {
    padding: 0.5rem;
    background: var(--color-surface);
    border-radius: 12px;
    border: 1px solid var(--color-border);
    transition: all 0.2s ease-in-out;
    margin: 0.3rem 0;

    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
    }

    .feature-title {
      font-size: 1.125rem;
      font-weight: 600;
      color: var(--color-text-primary);
      margin-bottom: 0.5rem;
      display: block;
    }

    .feature-desc {
      font-size: 0.875rem;
      color: var(--color-text-secondary);
      line-height: 1.5;
      display: block;
    }
  }
}

.section-title {
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--color-primary);
  display: inline-block;
}

.trending-crypto {
  margin-bottom: 2rem;

  .crypto-card {
    padding: 1rem;
    background: var(--color-surface);
    border-radius: 8px;
    border: 1px solid var(--color-border);
    transition: all 0.2s ease-in-out;

    &:hover {
      transform: translateY(-1px);
      box-shadow: var(--shadow-sm);
    }

    .crypto-header {
      display: flex;
      align-items: center;
      margin-bottom: 0.75rem;

      .crypto-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        margin-right: 0.5rem;
      }

      .crypto-info {
        .crypto-symbol {
          font-size: 1rem;
          font-weight: 600;
          color: var(--color-text-primary);
          display: block;
        }

        .crypto-name {
          font-size: 0.75rem;
          color: var(--color-text-secondary);
          display: block;
        }
      }
    }

    .crypto-price {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--color-primary);
      margin-bottom: 0.25rem;
      display: block;
    }

    .crypto-change {
      font-size: 0.875rem;
      font-weight: 500;
      display: block;

      &.positive {
        color: var(--color-success);
      }

      &.negative {
        color: var(--color-error);
      }
    }
  }
}

.trending-news {
  margin-bottom: 2rem;

  .news-card {
    padding: 1rem;
    background: var(--color-surface);
    border-radius: 8px;
    border: 1px solid var(--color-border);
    margin-bottom: 1rem;
    transition: all 0.2s ease-in-out;

    &:hover {
      transform: translateY(-1px);
      box-shadow: var(--shadow-sm);
    }

    .news-title {
      font-size: 1rem;
      font-weight: 600;
      color: var(--color-text-primary);
      margin-bottom: 0.5rem;
      display: block;
      line-height: 1.4;
    }

    .news-summary {
      font-size: 0.875rem;
      color: var(--color-text-secondary);
      margin-bottom: 0.5rem;
      display: block;
      line-height: 1.5;
    }

    .news-meta {
      font-size: 0.75rem;
      color: var(--color-text-secondary);
      display: block;
    }

    .news-image {
      width: 100%;
      border-radius: 4px;
      overflow: hidden;
    }
  }
}

.platform-tips {
  padding: 1rem;
  background: var(--color-surface);
  border-radius: 8px;
  border: 1px solid var(--color-border);
  text-align: center;
  margin-top: 2rem;

  .tips-text {
    font-size: 0.875rem;
    color: var(--color-text-secondary);
    display: block;
  }
}
