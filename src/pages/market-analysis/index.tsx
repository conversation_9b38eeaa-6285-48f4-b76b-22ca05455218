import { Button, Input, ScrollView, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useEffect, useState } from 'react';
import { NavigationBar } from '../../components/Navigation';
import { dataSelectors, useDataStore } from '../../stores/dataStore';
import './index.scss';

// 定义选项类型
interface ModelOption {
  value: string;
  label: string;
}

interface PreferenceOption {
  value: string;
  label: string;
}

interface UserTypeOption {
  value: string;
  label: string;
}

function MarketAnalysis() {
  // 状态管理
  const [selectedCoins, setSelectedCoins] = useState<string[]>([]);
  const [selectedModel, setSelectedModel] = useState('deepseek-chat');
  const [selectedPreference, setSelectedPreference] = useState('');
  const [selectedUserType, setSelectedUserType] = useState('');

  // 搜索相关状态
  const [coinSearchText, setCoinSearchText] = useState('');
  const [showCoinDropdown, setShowCoinDropdown] = useState(false);

  // 其他下拉框状态
  const [showModelDropdown, setShowModelDropdown] = useState(false);
  const [showPreferenceDropdown, setShowPreferenceDropdown] = useState(false);
  const [showUserTypeDropdown, setShowUserTypeDropdown] = useState(false);

  // 从dataStore获取状态
  const availableCoins = useDataStore(dataSelectors.getAvailableCoins());
  const coinsLoading = useDataStore(dataSelectors.getCoinsLoading());
  const coinsError = useDataStore(dataSelectors.getCoinsError());
  const analysisResult = useDataStore(dataSelectors.getAnalysisResult());
  const analysisLoading = useDataStore(dataSelectors.getAnalysisLoading());
  const analysisError = useDataStore(dataSelectors.getAnalysisError());
  const { fetchAvailableCoins, performAnalysis } = useDataStore();

  // 选项数据
  const modelOptions: ModelOption[] = [
    { value: 'deepseek-chat', label: 'DeepSeek Chat' },
  ];

  const preferenceOptions: PreferenceOption[] = [
    { value: 'aggressive', label: '激进' },
    { value: 'conservative', label: '保守' },
    { value: 'neutral', label: '中性' },
  ];

  const userTypeOptions: UserTypeOption[] = [
    { value: 'trader', label: '交易员' },
    { value: 'manager', label: '管理层' },
    { value: 'public', label: '大众用户' },
  ];

  // 获取币种数据
  useEffect(() => {
    fetchAvailableCoins();
  }, [fetchAvailableCoins]);

  // 过滤币种列表
  const filteredCoins =
    availableCoins?.filter(
      coin =>
        coin.name.toLowerCase().includes(coinSearchText.toLowerCase()) ||
        coin.symbol.toLowerCase().includes(coinSearchText.toLowerCase())
    ) || [];

  // 处理币种选择
  const handleCoinSelect = (coinId: string) => {
    if (selectedCoins.includes(coinId)) {
      setSelectedCoins(selectedCoins.filter(id => id !== coinId));
    } else {
      setSelectedCoins([...selectedCoins, coinId]);
    }
    // 选择后清空搜索文本并关闭下拉框
    setCoinSearchText('');
    setShowCoinDropdown(false);
  };

  // 移除选中的币种
  const removeCoin = (coinId: string) => {
    setSelectedCoins(selectedCoins.filter(id => id !== coinId));
  };

  // 分析功能
  const handleAnalyze = () => {
    if (selectedCoins.length === 0) {
      Taro.showToast({
        title: '请选择至少一个币种',
        icon: 'error',
      });
      return;
    }

    if (!selectedPreference) {
      Taro.showToast({
        title: '请选择投资偏好',
        icon: 'error',
      });
      return;
    }

    if (!selectedUserType) {
      Taro.showToast({
        title: '请选择用户类型',
        icon: 'error',
      });
      return;
    }

    // 分析请求
    const analysisRequest = {
      coins: selectedCoins,
      model: selectedModel,
      provider: 'deepseek',
      preference: selectedPreference,
      user_type: selectedUserType,
    };

    performAnalysis(analysisRequest);

    Taro.showToast({
      title: '正在分析中...',
      icon: 'loading',
    });
  };

  // 返回首页
  const handleBack = () => {
    Taro.navigateBack();
  };

  // 关闭所有下拉框
  const closeAllDropdowns = () => {
    setShowCoinDropdown(false);
    setShowModelDropdown(false);
    setShowPreferenceDropdown(false);
    setShowUserTypeDropdown(false);
  };

  // 渲染模型选择器
  const renderModelSelector = () => {
    return (
      <View className='market-analysis__dropdown-container'>
        <View
          className='market-analysis__selector'
          onClick={() => setShowModelDropdown(!showModelDropdown)}
        >
          <Text className='market-analysis__selector-text'>
            {modelOptions.find(opt => opt.value === selectedModel)?.label ||
              'DeepSeek Chat'}
          </Text>
          <Text className='market-analysis__selector-arrow'>
            {showModelDropdown ? '▲' : '▼'}
          </Text>
        </View>

        {showModelDropdown && (
          <View className='market-analysis__dropdown-list'>
            {modelOptions.map(option => (
              <View
                key={option.value}
                className={`market-analysis__dropdown-item ${
                  selectedModel === option.value
                    ? 'market-analysis__dropdown-item--selected'
                    : ''
                }`}
                onClick={() => {
                  setSelectedModel(option.value);
                  setShowModelDropdown(false);
                }}
              >
                <Text>{option.label}</Text>
                {selectedModel === option.value && (
                  <Text className='market-analysis__coin-selected'>✓</Text>
                )}
              </View>
            ))}
          </View>
        )}
      </View>
    );
  };

  // 渲染投资偏好选择器
  const renderPreferenceSelector = () => {
    return (
      <View className='market-analysis__dropdown-container'>
        <View
          className='market-analysis__selector'
          onClick={() => setShowPreferenceDropdown(!showPreferenceDropdown)}
        >
          <Text className='market-analysis__selector-text'>
            {selectedPreference
              ? preferenceOptions.find(opt => opt.value === selectedPreference)
                  ?.label
              : '请选择投资偏好'}
          </Text>
          <Text className='market-analysis__selector-arrow'>
            {showPreferenceDropdown ? '▲' : '▼'}
          </Text>
        </View>

        {showPreferenceDropdown && (
          <View className='market-analysis__dropdown-list'>
            {preferenceOptions.map(option => (
              <View
                key={option.value}
                className={`market-analysis__dropdown-item ${
                  selectedPreference === option.value
                    ? 'market-analysis__dropdown-item--selected'
                    : ''
                }`}
                onClick={() => {
                  setSelectedPreference(option.value);
                  setShowPreferenceDropdown(false);
                }}
              >
                <Text>{option.label}</Text>
                {selectedPreference === option.value && (
                  <Text className='market-analysis__coin-selected'>✓</Text>
                )}
              </View>
            ))}
          </View>
        )}
      </View>
    );
  };

  // 渲染用户类型选择器
  const renderUserTypeSelector = () => {
    return (
      <View className='market-analysis__dropdown-container'>
        <View
          className='market-analysis__selector'
          onClick={() => setShowUserTypeDropdown(!showUserTypeDropdown)}
        >
          <Text className='market-analysis__selector-text'>
            {selectedUserType
              ? userTypeOptions.find(opt => opt.value === selectedUserType)
                  ?.label
              : '请选择用户类型'}
          </Text>
          <Text className='market-analysis__selector-arrow'>
            {showUserTypeDropdown ? '▲' : '▼'}
          </Text>
        </View>

        {showUserTypeDropdown && (
          <View className='market-analysis__dropdown-list'>
            {userTypeOptions.map(option => (
              <View
                key={option.value}
                className={`market-analysis__dropdown-item ${
                  selectedUserType === option.value
                    ? 'market-analysis__dropdown-item--selected'
                    : ''
                }`}
                onClick={() => {
                  setSelectedUserType(option.value);
                  setShowUserTypeDropdown(false);
                }}
              >
                <Text>{option.label}</Text>
                {selectedUserType === option.value && (
                  <Text className='market-analysis__coin-selected'>✓</Text>
                )}
              </View>
            ))}
          </View>
        )}
      </View>
    );
  };

  // 渲染币种选择器（下拉框 + 搜索）
  const renderCoinSelector = () => {
    return (
      <View className='market-analysis__dropdown-container'>
        {/* 搜索输入框 */}
        <View className='market-analysis__search-container'>
          <Input
            className='market-analysis__search-input'
            placeholder={
              selectedCoins.length > 0
                ? `已选择 ${selectedCoins.length} 个币种`
                : '搜索币种名称或符号...'
            }
            value={coinSearchText}
            onInput={e => setCoinSearchText(e.detail.value)}
            onFocus={() => setShowCoinDropdown(true)}
          />
          <View
            className='market-analysis__search-toggle'
            onClick={() => setShowCoinDropdown(!showCoinDropdown)}
          >
            {showCoinDropdown ? '▲' : '▼'}
          </View>
        </View>

        {/* 下拉选项列表 */}
        {showCoinDropdown && (
          <View className='market-analysis__dropdown-list'>
            {coinsLoading ? (
              <View className='market-analysis__dropdown-item market-analysis__dropdown-item--loading'>
                加载币种中...
              </View>
            ) : filteredCoins.length === 0 ? (
              <View className='market-analysis__dropdown-item market-analysis__dropdown-item--empty'>
                {coinSearchText ? '未找到匹配的币种' : '暂无币种数据'}
              </View>
            ) : (
              <ScrollView
                className='market-analysis__dropdown-scroll'
                scrollY
                style={{ maxHeight: '200px' }}
              >
                {filteredCoins.map(coin => (
                  <View
                    key={coin.id}
                    className={`market-analysis__dropdown-item ${
                      selectedCoins.includes(coin.id)
                        ? 'market-analysis__dropdown-item--selected'
                        : ''
                    }`}
                    onClick={() => handleCoinSelect(coin.id)}
                  >
                    <View className='market-analysis__coin-info'>
                      <Text className='market-analysis__coin-name'>
                        {coin.name}
                      </Text>
                      <Text className='market-analysis__coin-symbol'>
                        ({coin.symbol})
                      </Text>
                    </View>
                    {selectedCoins.includes(coin.id) && (
                      <Text className='market-analysis__coin-selected'>✓</Text>
                    )}
                  </View>
                ))}
              </ScrollView>
            )}
          </View>
        )}

        {/* 已选择的币种标签 */}
        <View className='market-analysis__selected-tags'>
          {selectedCoins.map(coinId => {
            const coin = availableCoins?.find(c => c.id === coinId);
            return coin ? (
              <View key={coinId} className='market-analysis__tag'>
                <Text className='market-analysis__tag-text'>
                  {coin.name} ({coin.symbol})
                </Text>
                <Text
                  className='market-analysis__tag-close'
                  onClick={() => removeCoin(coinId)}
                >
                  ×
                </Text>
              </View>
            ) : null;
          })}
          {selectedCoins.length === 0 && (
            <View className='market-analysis__placeholder'>
              请搜索并选择币种
            </View>
          )}
        </View>
      </View>
    );
  };

  return (
    <View className='market-analysis'>
      {/* 头部导航栏 */}
      <NavigationBar title='市场分析' showBack onBackClick={handleBack} />

      {/* 主要内容区域 */}
      <View className='market-analysis__content'>
        {/* 币种选择器 */}
        <View className='market-analysis__form-item'>
          <Text className='market-analysis__label'>币种选择（多选）</Text>
          {renderCoinSelector()}
        </View>

        {/* 模型选择器 */}
        <View className='market-analysis__form-item'>
          <Text className='market-analysis__label'>AI模型</Text>
          {renderModelSelector()}
        </View>

        {/* 投资偏好选择器 */}
        <View className='market-analysis__form-item'>
          <Text className='market-analysis__label'>投资偏好</Text>
          {renderPreferenceSelector()}
        </View>

        {/* 用户类型选择器 */}
        <View className='market-analysis__form-item'>
          <Text className='market-analysis__label'>用户类型</Text>
          {renderUserTypeSelector()}
        </View>

        {/* 分析结果展示 */}
        {analysisResult && (
          <View className='market-analysis__result'>
            <Text className='market-analysis__result-title'>分析结果</Text>
            <View className='market-analysis__result-content'>
              {JSON.stringify(analysisResult, null, 2)}
            </View>
          </View>
        )}

        {analysisError && (
          <View className='market-analysis__error'>
            <Text>分析失败: {analysisError}</Text>
          </View>
        )}
      </View>

      {/* 分析按钮 */}
      <View className='market-analysis__analyze-section'>
        <Button
          className='market-analysis__analyze-btn'
          onClick={handleAnalyze}
          disabled={
            selectedCoins.length === 0 ||
            !selectedPreference ||
            !selectedUserType ||
            analysisLoading
          }
        >
          {analysisLoading ? '分析中...' : '开始分析'}
        </Button>
      </View>
    </View>
  );
}

export default MarketAnalysis;
