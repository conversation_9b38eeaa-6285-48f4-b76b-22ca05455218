import { ScrollView, Text, View } from '@tarojs/components';
import React, { useState } from 'react';
import Container from '../../components/Layout/Container';
import { NavigationBar } from '../../components/Navigation';
import TabBar from '../../components/TabBar';
import {
  CryptoDrawer,
  LanguageSelector,
  ProfileSkeleton,
  SubscriptionCard,
} from './components';
import './index.scss';

const Profile: React.FC = () => {
  const [showCryptoDrawer, setShowCryptoDrawer] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState('zh-CN');

  // 模拟关注的加密货币数据
  const [followedCryptos, setFollowedCryptos] = useState([
    { id: 'bitcoin', name: 'BitCoin', symbol: 'BTC', followed: true },
    { id: 'dogecoin', name: '<PERSON><PERSON>', symbol: 'DOGE', followed: true },
    { id: 'ethereum', name: 'Eth', symbol: 'ETH', followed: true },
    { id: 'pepe', name: 'PEPE', symbol: 'PEPE', followed: true },
    { id: 'solana', name: 'SOL', symbol: 'SOL', followed: false },
  ]);

  // 处理加密货币关注状态切换
  const handleCryptoToggle = (cryptoId: string) => {
    setFollowedCryptos(prev =>
      prev.map(crypto =>
        crypto.id === cryptoId
          ? { ...crypto, followed: !crypto.followed }
          : crypto
      )
    );
  };

  // 处理语言切换
  const handleLanguageChange = (language: string) => {
    setSelectedLanguage(language);
    // 这里可以添加实际的语言切换逻辑
    console.log('切换语言到:', language);
  };

  return (
    <View className='profile-page'>
      <NavigationBar title='我的' />

      <Container>
        <ScrollView
          className='profile-content'
          scrollY
          enhanced
          showScrollbar={false}
        >
          {/* 个人信息区域 - 使用骨架屏 */}
          <View className='profile-section'>
            <ProfileSkeleton />
          </View>

          {/* 订阅信息卡片 */}
          <View className='profile-section'>
            <SubscriptionCard />
          </View>

          {/* 关注的crypto */}
          <View className='profile-section'>
            <View
              className='profile-item'
              onClick={() => setShowCryptoDrawer(true)}
            >
              <Text className='profile-item__label'>关注的crypto</Text>
              <Text className='profile-item__arrow'>›</Text>
            </View>
          </View>

          {/* 语言选择 */}
          <View className='profile-section'>
            <LanguageSelector
              selectedLanguage={selectedLanguage}
              onLanguageChange={handleLanguageChange}
            />
          </View>
        </ScrollView>
      </Container>

      {/* 加密货币抽屉 */}
      <CryptoDrawer
        visible={showCryptoDrawer}
        cryptos={followedCryptos}
        onClose={() => setShowCryptoDrawer(false)}
        onCryptoToggle={handleCryptoToggle}
      />

      <TabBar current={2} />
    </View>
  );
};

export default Profile;
