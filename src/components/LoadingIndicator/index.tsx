import { Text, View } from '@tarojs/components';
import React from 'react';
import { useLoadingStore } from '../../stores/loadingStore';
import './index.scss';

interface LoadingIndicatorProps {
  loadingId?: string;
  showMessage?: boolean;
  showProgress?: boolean;
  size?: 'small' | 'medium' | 'large';
  type?: 'spinner' | 'dots' | 'pulse' | 'bar';
  className?: string;
}

// 基础加载指示器
export const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({
  loadingId,
  showMessage = true,
  showProgress = true,
  size = 'medium',
  type = 'spinner',
  className = '',
}) => {
  const { loadingStates, globalProgress } = useLoadingStore();

  const loadingState = loadingId ? loadingStates[loadingId] : null;
  const isLoading = loadingId
    ? !!loadingState
    : Object.keys(loadingStates).length > 0;
  const progress = loadingId ? loadingState?.progress : globalProgress;
  const message = loadingId ? loadingState?.message : '加载中...';

  if (!isLoading) return null;

  const renderSpinner = () => (
    <View className={`loading-spinner loading-spinner--${size}`}>
      <View className='loading-spinner__circle' />
    </View>
  );

  const renderDots = () => (
    <View className={`loading-dots loading-dots--${size}`}>
      <View className='loading-dots__dot' />
      <View className='loading-dots__dot' />
      <View className='loading-dots__dot' />
    </View>
  );

  const renderPulse = () => (
    <View className={`loading-pulse loading-pulse--${size}`}>
      <View className='loading-pulse__circle' />
    </View>
  );

  const renderBar = () => (
    <View className={`loading-bar loading-bar--${size}`}>
      <View
        className='loading-bar__fill'
        style={{ width: `${progress || 0}%` }}
      />
    </View>
  );

  const renderLoader = () => {
    switch (type) {
      case 'dots':
        return renderDots();
      case 'pulse':
        return renderPulse();
      case 'bar':
        return renderBar();
      default:
        return renderSpinner();
    }
  };

  return (
    <View
      className={`loading-indicator loading-indicator--${size} ${className}`}
    >
      {renderLoader()}
      {showMessage && message && (
        <Text className='loading-indicator__message'>{message}</Text>
      )}
      {showProgress && progress !== undefined && type !== 'bar' && (
        <Text className='loading-indicator__progress'>
          {Math.round(progress)}%
        </Text>
      )}
    </View>
  );
};

// 全局加载覆盖层
export const GlobalLoadingOverlay: React.FC<{
  showProgress?: boolean;
  showMessage?: boolean;
  backdrop?: boolean;
}> = ({ showProgress = true, showMessage = true, backdrop = true }) => {
  const { loadingStates, globalProgress, globalLoading } = useLoadingStore();

  if (!globalLoading) return null;

  const activeLoadings = Object.values(loadingStates);
  const currentLoading = activeLoadings[0]; // 显示第一个加载状态
  const message = currentLoading?.message || '加载中...';

  return (
    <View
      className={`global-loading-overlay ${
        backdrop ? 'global-loading-overlay--backdrop' : ''
      }`}
    >
      <View className='global-loading-content'>
        <LoadingIndicator
          showMessage={false}
          showProgress={false}
          size='large'
          type='spinner'
        />
        {showMessage && (
          <Text className='global-loading-message'>{message}</Text>
        )}
        {showProgress && (
          <View className='global-loading-progress'>
            <View className='global-loading-progress-bar'>
              <View
                className='global-loading-progress-fill'
                style={{ width: `${globalProgress}%` }}
              />
            </View>
            <Text className='global-loading-progress-text'>
              {Math.round(globalProgress)}%
            </Text>
          </View>
        )}
      </View>
    </View>
  );
};

// 内联加载指示器
export const InlineLoading: React.FC<{
  loadingId?: string;
  text?: string;
  size?: 'small' | 'medium';
}> = ({ loadingId, text = '加载中...', size = 'small' }) => {
  const { loadingStates } = useLoadingStore();
  const isLoading = loadingId
    ? !!loadingStates[loadingId]
    : Object.keys(loadingStates).length > 0;

  if (!isLoading) return null;

  return (
    <View className={`inline-loading inline-loading--${size}`}>
      <LoadingIndicator
        loadingId={loadingId}
        showMessage={false}
        showProgress={false}
        size={size}
        type='dots'
      />
      <Text className='inline-loading__text'>{text}</Text>
    </View>
  );
};

// 按钮加载状态
export const ButtonLoading: React.FC<{
  loading?: boolean;
  children: React.ReactNode;
  disabled?: boolean;
  onClick?: () => void;
  className?: string;
  size?: 'small' | 'medium' | 'large';
  type?: 'primary' | 'secondary' | 'ghost';
}> = ({
  loading = false,
  children,
  disabled = false,
  onClick,
  className = '',
  size = 'medium',
  type = 'primary',
}) => {
  return (
    <View
      className={`button-loading button-loading--${size} button-loading--${type} ${
        loading || disabled ? 'button-loading--disabled' : ''
      } ${className}`}
      onClick={loading || disabled ? undefined : onClick}
    >
      {loading && (
        <LoadingIndicator
          showMessage={false}
          showProgress={false}
          size='small'
          type='spinner'
          className='button-loading__indicator'
        />
      )}
      <View
        className={`button-loading__content ${
          loading ? 'button-loading__content--loading' : ''
        }`}
      >
        {children}
      </View>
    </View>
  );
};

// 卡片加载状态
export const CardLoading: React.FC<{
  loading?: boolean;
  children: React.ReactNode;
  height?: number;
  className?: string;
}> = ({ loading = false, children, height = 200, className = '' }) => {
  if (loading) {
    return (
      <View
        className={`card-loading ${className}`}
        style={{ height: `${height}px` }}
      >
        <LoadingIndicator
          showMessage={true}
          showProgress={false}
          size='medium'
          type='pulse'
        />
      </View>
    );
  }

  return <>{children}</>;
};

// 页面加载状态
export const PageLoading: React.FC<{
  loading?: boolean;
  children: React.ReactNode;
  message?: string;
  showProgress?: boolean;
}> = ({
  loading = false,
  children,
  message = '页面加载中...',
  showProgress = false,
}) => {
  if (loading) {
    return (
      <View className='page-loading'>
        <LoadingIndicator
          showMessage={true}
          showProgress={showProgress}
          size='large'
          type='spinner'
        />
        <Text className='page-loading__message'>{message}</Text>
      </View>
    );
  }

  return <>{children}</>;
};

// 列表加载更多
export const LoadMore: React.FC<{
  loading?: boolean;
  hasMore?: boolean;
  onLoadMore?: () => void;
  text?: string;
  loadingText?: string;
  noMoreText?: string;
}> = ({
  loading = false,
  hasMore = true,
  onLoadMore,
  text = '加载更多',
  loadingText = '加载中...',
  noMoreText = '没有更多了',
}) => {
  const handleClick = () => {
    if (!loading && hasMore && onLoadMore) {
      onLoadMore();
    }
  };

  return (
    <View
      className={`load-more ${!hasMore ? 'load-more--disabled' : ''}`}
      onClick={handleClick}
    >
      {loading ? (
        <InlineLoading text={loadingText} />
      ) : (
        <Text className='load-more__text'>{hasMore ? text : noMoreText}</Text>
      )}
    </View>
  );
};

export default LoadingIndicator;
