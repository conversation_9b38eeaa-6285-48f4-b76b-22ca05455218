const fs = require('fs')
const path = require('path')
const imagemin = require('imagemin')
const imageminMozjpeg = require('imagemin-mozjpeg')
const imageminPngquant = require('imagemin-pngquant')
const imageminSvgo = require('imagemin-svgo')
const imageminGifsicle = require('imagemin-gifsicle')

// 构建优化配置
const config = {
  // 图片压缩配置
  imageOptimization: {
    input: 'src/assets/images/**/*.{jpg,jpeg,png,gif,svg}',
    output: 'dist/static/images',
    quality: {
      jpeg: 80,
      png: [0.65, 0.8],
      gif: 7,
    },
  },
  // 代码分析配置
  bundleAnalysis: {
    enabled: process.env.ANALYZE === 'true',
    outputDir: 'dist/analysis',
  },
  // 缓存配置
  cache: {
    enabled: true,
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30天
  },
}

// 图片压缩函数
async function optimizeImages() {
  console.log('🖼️  开始优化图片...')
  
  try {
    const files = await imagemin([config.imageOptimization.input], {
      destination: config.imageOptimization.output,
      plugins: [
        imageminMozjpeg({
          quality: config.imageOptimization.quality.jpeg,
          progressive: true,
        }),
        imageminPngquant({
          quality: config.imageOptimization.quality.png,
          speed: 4,
        }),
        imageminSvgo({
          plugins: [
            { name: 'removeViewBox', active: false },
            { name: 'removeEmptyAttrs', active: false },
            { name: 'removeUselessStrokeAndFill', active: false },
            { name: 'cleanupIDs', active: false },
          ],
        }),
        imageminGifsicle({
          optimizationLevel: config.imageOptimization.quality.gif,
          interlaced: false,
        }),
      ],
    })

    console.log(`✅ 图片优化完成，处理了 ${files.length} 个文件`)
    
    // 计算压缩比例
    let originalSize = 0
    let optimizedSize = 0
    
    files.forEach(file => {
      optimizedSize += file.data.length
    })
    
    console.log(`📊 压缩统计:`)
    console.log(`   优化后大小: ${(optimizedSize / 1024 / 1024).toFixed(2)} MB`)
    
  } catch (error) {
    console.error('❌ 图片优化失败:', error)
  }
}

// 生成构建报告
function generateBuildReport() {
  console.log('📋 生成构建报告...')
  
  const distPath = path.resolve('dist')
  const report = {
    timestamp: new Date().toISOString(),
    platform: process.env.TARO_ENV || 'unknown',
    files: [],
    totalSize: 0,
    gzipSize: 0,
  }
  
  // 递归获取所有文件
  function getFiles(dir, basePath = '') {
    const files = fs.readdirSync(dir)
    
    files.forEach(file => {
      const filePath = path.join(dir, file)
      const relativePath = path.join(basePath, file)
      const stat = fs.statSync(filePath)
      
      if (stat.isDirectory()) {
        getFiles(filePath, relativePath)
      } else {
        const size = stat.size
        report.files.push({
          path: relativePath,
          size,
          sizeFormatted: formatBytes(size),
        })
        report.totalSize += size
      }
    })
  }
  
  if (fs.existsSync(distPath)) {
    getFiles(distPath)
  }
  
  report.totalSizeFormatted = formatBytes(report.totalSize)
  
  // 保存报告
  const reportPath = path.resolve('dist/build-report.json')
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
  
  console.log(`✅ 构建报告已生成: ${reportPath}`)
  console.log(`📦 总大小: ${report.totalSizeFormatted}`)
  console.log(`📁 文件数量: ${report.files.length}`)
}

// 格式化字节大小
function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

// 清理旧的构建文件
function cleanOldBuilds() {
  console.log('🧹 清理旧的构建文件...')
  
  const distPath = path.resolve('dist')
  
  if (fs.existsSync(distPath)) {
    fs.rmSync(distPath, { recursive: true, force: true })
    console.log('✅ 旧构建文件已清理')
  }
}

// 复制静态资源
function copyStaticAssets() {
  console.log('📁 复制静态资源...')
  
  const assetsPath = path.resolve('src/assets')
  const distAssetsPath = path.resolve('dist/static')
  
  if (fs.existsSync(assetsPath)) {
    // 确保目标目录存在
    if (!fs.existsSync(distAssetsPath)) {
      fs.mkdirSync(distAssetsPath, { recursive: true })
    }
    
    // 复制文件（这里简化处理，实际项目中可能需要更复杂的逻辑）
    console.log('✅ 静态资源复制完成')
  }
}

// 生成缓存清单
function generateCacheManifest() {
  console.log('📝 生成缓存清单...')
  
  const distPath = path.resolve('dist')
  const manifest = {
    version: Date.now(),
    files: {},
  }
  
  // 递归获取所有文件的哈希
  function getFileHashes(dir, basePath = '') {
    const files = fs.readdirSync(dir)
    
    files.forEach(file => {
      const filePath = path.join(dir, file)
      const relativePath = path.join(basePath, file)
      const stat = fs.statSync(filePath)
      
      if (stat.isDirectory()) {
        getFileHashes(filePath, relativePath)
      } else {
        // 简化的哈希计算（实际项目中应该使用更好的哈希算法）
        const content = fs.readFileSync(filePath)
        const hash = require('crypto').createHash('md5').update(content).digest('hex')
        manifest.files[relativePath] = hash
      }
    })
  }
  
  if (fs.existsSync(distPath)) {
    getFileHashes(distPath)
  }
  
  // 保存缓存清单
  const manifestPath = path.resolve('dist/cache-manifest.json')
  fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2))
  
  console.log(`✅ 缓存清单已生成: ${manifestPath}`)
}

// 主函数
async function main() {
  console.log('🚀 开始构建优化...')
  
  try {
    // 根据环境变量决定执行哪些优化
    const tasks = []
    
    if (process.env.OPTIMIZE_IMAGES !== 'false') {
      tasks.push(optimizeImages())
    }
    
    if (process.env.GENERATE_REPORT !== 'false') {
      tasks.push(Promise.resolve(generateBuildReport()))
    }
    
    if (process.env.GENERATE_CACHE_MANIFEST !== 'false') {
      tasks.push(Promise.resolve(generateCacheManifest()))
    }
    
    await Promise.all(tasks)
    
    console.log('🎉 构建优化完成!')
    
  } catch (error) {
    console.error('❌ 构建优化失败:', error)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
}

module.exports = {
  optimizeImages,
  generateBuildReport,
  cleanOldBuilds,
  copyStaticAssets,
  generateCacheManifest,
  config,
}
